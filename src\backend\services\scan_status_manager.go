package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"adgitops-ui/src/backend/models"
)

// ScanStatusManager manages scan status
type ScanStatusManager struct {
	statusMap      map[string]models.UsageScanStatus // Map of groupName:repoID to status
	resultsMap     map[string][]models.UsageResult   // Map of groupName:repoID to results
	heartbeatMap   map[string]time.Time              // Map of groupName:repoID to last heartbeat
	statusMutex    sync.RWMutex
	resultsMutex   sync.RWMutex
	heartbeatMutex sync.RWMutex
	statusDir      string        // Directory for status files
	resultsDir     string        // Directory for results files
	scanTimeout    time.Duration // Maximum time a scan can run before being considered orphaned
	startupTime    time.Time     // Time when the manager was created (for detecting stale tasks)
}

// NewScanStatusManager creates a new scan status manager
func NewScanStatusManager(dataDir string) *ScanStatusManager {
	statusDir := filepath.Join(dataDir, "usage-results", "status")
	resultsDir := filepath.Join(dataDir, "usage-results", "results")

	// Create directories
	if err := os.MkdirAll(statusDir, 0755); err != nil {
		log.Printf("Warning: Failed to create status directory: %v", err)
	}
	if err := os.MkdirAll(resultsDir, 0755); err != nil {
		log.Printf("Warning: Failed to create results directory: %v", err)
	}

	manager := &ScanStatusManager{
		statusMap:    make(map[string]models.UsageScanStatus),
		resultsMap:   make(map[string][]models.UsageResult),
		heartbeatMap: make(map[string]time.Time),
		statusDir:    statusDir,
		resultsDir:   resultsDir,
		scanTimeout:  30 * time.Minute, // Default 30 minute timeout for orphaned scans
		startupTime:  time.Now(),       // Record startup time for stale task detection
	}

	// Load existing status and results
	manager.loadAllStatus()
	manager.loadAllResults()

	// Perform crash recovery on startup
	manager.recoverOrphanedScans()

	return manager
}

// GetStatus gets the status of a scan
func (m *ScanStatusManager) GetStatus(groupName, repoID string) (models.UsageScanStatus, error) {
	key := m.getKey(groupName, repoID)

	m.statusMutex.RLock()
	defer m.statusMutex.RUnlock()

	status, exists := m.statusMap[key]
	if !exists {
		// Try to load from disk
		if loadedStatus, err := m.loadStatusFromDisk(groupName, repoID); err == nil {
			return loadedStatus, nil
		}
		return models.UsageScanStatus{}, fmt.Errorf("scan status not found for group %s in repo %s", groupName, repoID)
	}

	return status, nil
}

// UpdateStatus updates the status of a scan
func (m *ScanStatusManager) UpdateStatus(status models.UsageScanStatus) error {
	key := m.getKey(status.GroupName, status.RepoID)

	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	m.statusMap[key] = status

	// If scan is no longer in progress, remove heartbeat
	if !status.InProgress {
		m.RemoveHeartbeat(status.GroupName, status.RepoID)
	}

	// Save to disk
	if err := m.saveStatusToDisk(status); err != nil {
		log.Printf("Warning: Failed to save status to disk: %v", err)
		return err
	}

	return nil
}

// AddResults adds results to a scan
func (m *ScanStatusManager) AddResults(groupName, repoID, sourceID string, results []models.UsageResult) error {
	key := m.getKey(groupName, repoID)

	m.resultsMutex.Lock()
	defer m.resultsMutex.Unlock()

	// Add results to memory
	if existing, exists := m.resultsMap[key]; exists {
		m.resultsMap[key] = append(existing, results...)
	} else {
		m.resultsMap[key] = results
	}

	// Update scan status
	m.statusMutex.Lock()
	if status, exists := m.statusMap[key]; exists {
		// Add to completed sources
		found := false
		for _, completedID := range status.CompletedSources {
			if completedID == sourceID {
				found = true
				break
			}
		}
		if !found {
			status.CompletedSources = append(status.CompletedSources, sourceID)
		}

		// Remove from pending sources
		var newPending []string
		for _, pendingID := range status.PendingSources {
			if pendingID != sourceID {
				newPending = append(newPending, pendingID)
			}
		}
		status.PendingSources = newPending

		// Update counters
		status.SourcesScanned = len(status.CompletedSources)
		status.TotalUsages += len(results)

		// Check if scan is complete
		if len(status.PendingSources) == 0 {
			status.InProgress = false
		}

		m.statusMap[key] = status

		// Save status to disk
		if err := m.saveStatusToDisk(status); err != nil {
			log.Printf("Warning: Failed to save updated status to disk: %v", err)
		}
	}
	m.statusMutex.Unlock()

	// Save results to disk
	if err := m.saveResultsToDisk(groupName, repoID, m.resultsMap[key]); err != nil {
		log.Printf("Warning: Failed to save results to disk: %v", err)
		return err
	}

	return nil
}

// GetResults gets the results of a scan
func (m *ScanStatusManager) GetResults(groupName, repoID string, page, pageSize int) (models.UsageResultList, error) {
	key := m.getKey(groupName, repoID)

	m.resultsMutex.RLock()
	defer m.resultsMutex.RUnlock()

	results, exists := m.resultsMap[key]
	if !exists {
		// Try to load from disk
		if loadedResults, err := m.loadResultsFromDisk(groupName, repoID); err == nil {
			results = loadedResults
		} else {
			results = []models.UsageResult{}
		}
	}

	// Apply pagination
	total := len(results)
	start := (page - 1) * pageSize
	if start < 0 {
		start = 0
	}
	if start >= total {
		return models.UsageResultList{
			Results:  []models.UsageResult{},
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		}, nil
	}

	end := start + pageSize
	if end > total {
		end = total
	}

	paginatedResults := results[start:end]

	return models.UsageResultList{
		Results:  paginatedResults,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}, nil
}

// ClearResults clears the results of a scan
func (m *ScanStatusManager) ClearResults(groupName, repoID string) error {
	key := m.getKey(groupName, repoID)

	m.resultsMutex.Lock()
	delete(m.resultsMap, key)
	m.resultsMutex.Unlock()

	m.statusMutex.Lock()
	delete(m.statusMap, key)
	m.statusMutex.Unlock()

	// Remove files from disk
	statusFile := m.getStatusFilePath(groupName, repoID)
	resultsFile := m.getResultsFilePath(groupName, repoID)

	if err := os.Remove(statusFile); err != nil && !os.IsNotExist(err) {
		log.Printf("Warning: Failed to remove status file: %v", err)
	}

	if err := os.Remove(resultsFile); err != nil && !os.IsNotExist(err) {
		log.Printf("Warning: Failed to remove results file: %v", err)
	}

	return nil
}

// ClearFailedStatus clears the failed status of a scan, removing it from failed tasks list
func (m *ScanStatusManager) ClearFailedStatus(groupName, repoID string) error {
	key := m.getKey(groupName, repoID)

	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	if status, exists := m.statusMap[key]; exists {
		// Reset failed status - clear failed sources and mark as not in progress
		status.FailedSources = []models.SourceScanFailure{}
		status.InProgress = false
		m.statusMap[key] = status
	}

	return nil
}

// GetAllActiveScans returns all currently active scans
func (m *ScanStatusManager) GetAllActiveScans() ([]models.UsageScanStatus, error) {
	m.statusMutex.RLock()
	defer m.statusMutex.RUnlock()

	var activeScans []models.UsageScanStatus

	for _, status := range m.statusMap {
		if status.InProgress {
			activeScans = append(activeScans, status)
		}
	}

	return activeScans, nil
}

// GetAllScans returns all scan statuses (active and completed)
func (m *ScanStatusManager) GetAllScans() ([]models.UsageScanStatus, error) {
	m.statusMutex.RLock()
	defer m.statusMutex.RUnlock()

	var allScans []models.UsageScanStatus

	for _, status := range m.statusMap {
		allScans = append(allScans, status)
	}

	return allScans, nil
}

// GetFailedScans returns all scans that have failed sources or were marked as failed
func (m *ScanStatusManager) GetFailedScans() ([]models.UsageScanStatus, error) {
	m.statusMutex.RLock()
	defer m.statusMutex.RUnlock()

	var failedScans []models.UsageScanStatus

	for _, status := range m.statusMap {
		// A scan is considered failed if:
		// 1. It's not in progress and has failed sources, OR
		// 2. It's not in progress and has no completed sources but was started
		if !status.InProgress && (len(status.FailedSources) > 0 ||
			(len(status.CompletedSources) == 0 && status.SourcesScanned > 0)) {
			failedScans = append(failedScans, status)
		}
	}

	return failedScans, nil
}

// GetInterruptedScans returns scans that are marked as in progress but may be orphaned
func (m *ScanStatusManager) GetInterruptedScans() ([]models.UsageScanStatus, error) {
	m.statusMutex.RLock()
	m.heartbeatMutex.RLock()
	defer m.statusMutex.RUnlock()
	defer m.heartbeatMutex.RUnlock()

	var interruptedScans []models.UsageScanStatus
	now := time.Now()

	for key, status := range m.statusMap {
		if status.InProgress {
			// Check if scan might be orphaned
			isOrphaned := false

			// Check heartbeat
			if lastHeartbeat, exists := m.heartbeatMap[key]; exists {
				if now.Sub(lastHeartbeat) > m.scanTimeout {
					isOrphaned = true
				}
			} else {
				// No heartbeat found for an in-progress scan
				timeSinceStart := now.Sub(status.LastScanTime)
				if timeSinceStart > m.scanTimeout {
					isOrphaned = true
				}
			}

			if isOrphaned {
				interruptedScans = append(interruptedScans, status)
			}
		}
	}

	return interruptedScans, nil
}

// Helper methods

func (m *ScanStatusManager) getKey(groupName, repoID string) string {
	return fmt.Sprintf("%s:%s", groupName, repoID)
}

// UpdateSourceFailure updates the scan status when a source fails
func (m *ScanStatusManager) UpdateSourceFailure(groupName, repoID, sourceID, errorMessage string) error {
	key := m.getKey(groupName, repoID)

	// Update scan status
	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	if status, exists := m.statusMap[key]; exists {
		// Add to failed sources
		failure := models.SourceScanFailure{
			SourceID:   sourceID,
			SourceName: sourceID, // TODO: Get actual source name
			Error:      errorMessage,
			FailedAt:   time.Now(),
		}
		status.FailedSources = append(status.FailedSources, failure)

		// Remove from pending sources
		var newPending []string
		for _, pendingID := range status.PendingSources {
			if pendingID != sourceID {
				newPending = append(newPending, pendingID)
			}
		}
		status.PendingSources = newPending

		// Update counters
		status.SourcesScanned = len(status.CompletedSources) + len(status.FailedSources)

		// Check if scan is complete
		if len(status.PendingSources) == 0 {
			status.InProgress = false
		}

		m.statusMap[key] = status

		// Save status to disk
		if err := m.saveStatusToDisk(status); err != nil {
			log.Printf("Warning: Failed to save updated status to disk: %v", err)
			return err
		}
	}

	return nil
}

func (m *ScanStatusManager) getStatusFilePath(groupName, repoID string) string {
	filename := fmt.Sprintf("%s_%s_status.json", repoID, groupName)
	return filepath.Join(m.statusDir, filename)
}

func (m *ScanStatusManager) getResultsFilePath(groupName, repoID string) string {
	filename := fmt.Sprintf("%s_%s_results.json", repoID, groupName)
	return filepath.Join(m.resultsDir, filename)
}

func (m *ScanStatusManager) saveStatusToDisk(status models.UsageScanStatus) error {
	filePath := m.getStatusFilePath(status.GroupName, status.RepoID)

	data, err := json.MarshalIndent(status, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal status: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write status file: %w", err)
	}

	return nil
}

func (m *ScanStatusManager) loadStatusFromDisk(groupName, repoID string) (models.UsageScanStatus, error) {
	filePath := m.getStatusFilePath(groupName, repoID)

	data, err := os.ReadFile(filePath)
	if err != nil {
		return models.UsageScanStatus{}, fmt.Errorf("failed to read status file: %w", err)
	}

	var status models.UsageScanStatus
	if err := json.Unmarshal(data, &status); err != nil {
		return models.UsageScanStatus{}, fmt.Errorf("failed to unmarshal status: %w", err)
	}

	return status, nil
}

func (m *ScanStatusManager) saveResultsToDisk(groupName, repoID string, results []models.UsageResult) error {
	filePath := m.getResultsFilePath(groupName, repoID)

	resultsList := models.UsageResultList{
		Results:  results,
		Total:    len(results),
		Page:     1,
		PageSize: len(results),
	}

	data, err := json.MarshalIndent(resultsList, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal results: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write results file: %w", err)
	}

	return nil
}

func (m *ScanStatusManager) loadResultsFromDisk(groupName, repoID string) ([]models.UsageResult, error) {
	filePath := m.getResultsFilePath(groupName, repoID)

	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read results file: %w", err)
	}

	var resultsList models.UsageResultList
	if err := json.Unmarshal(data, &resultsList); err != nil {
		return nil, fmt.Errorf("failed to unmarshal results: %w", err)
	}

	return resultsList.Results, nil
}

func (m *ScanStatusManager) loadAllStatus() {
	// Load all status files from disk
	files, err := filepath.Glob(filepath.Join(m.statusDir, "*_status.json"))
	if err != nil {
		log.Printf("Warning: Failed to glob status files: %v", err)
		return
	}

	for _, file := range files {
		data, err := os.ReadFile(file)
		if err != nil {
			log.Printf("Warning: Failed to read status file %s: %v", file, err)
			continue
		}

		var status models.UsageScanStatus
		if err := json.Unmarshal(data, &status); err != nil {
			log.Printf("Warning: Failed to unmarshal status file %s: %v", file, err)
			continue
		}

		key := m.getKey(status.GroupName, status.RepoID)
		m.statusMap[key] = status
	}

	log.Printf("Loaded %d scan status records from disk", len(m.statusMap))
}

func (m *ScanStatusManager) loadAllResults() {
	// Load all results files from disk
	files, err := filepath.Glob(filepath.Join(m.resultsDir, "*_results.json"))
	if err != nil {
		log.Printf("Warning: Failed to glob results files: %v", err)
		return
	}

	for _, file := range files {
		data, err := os.ReadFile(file)
		if err != nil {
			log.Printf("Warning: Failed to read results file %s: %v", file, err)
			continue
		}

		var resultsList models.UsageResultList
		if err := json.Unmarshal(data, &resultsList); err != nil {
			log.Printf("Warning: Failed to unmarshal results file %s: %v", file, err)
			continue
		}

		// Extract groupName and repoID from filename
		// Filename format: {repoID}_{groupName}_results.json
		basename := filepath.Base(file)
		basename = basename[:len(basename)-len("_results.json")]

		// Find the first underscore to separate repoID and groupName
		parts := []string{}
		current := ""
		for i, char := range basename {
			if char == '_' && len(parts) == 0 {
				parts = append(parts, current)
				current = basename[i+1:]
				break
			}
			current += string(char)
		}
		if current != "" {
			parts = append(parts, current)
		}

		if len(parts) == 2 {
			repoID := parts[0]
			groupName := parts[1]
			key := m.getKey(groupName, repoID)
			m.resultsMap[key] = resultsList.Results
		}
	}

	log.Printf("Loaded %d scan results records from disk", len(m.resultsMap))
}

// UpdateHeartbeat updates the heartbeat for an active scan
func (m *ScanStatusManager) UpdateHeartbeat(groupName, repoID string) {
	key := m.getKey(groupName, repoID)

	m.heartbeatMutex.Lock()
	defer m.heartbeatMutex.Unlock()

	m.heartbeatMap[key] = time.Now()
}

// RemoveHeartbeat removes the heartbeat for a completed scan
func (m *ScanStatusManager) RemoveHeartbeat(groupName, repoID string) {
	key := m.getKey(groupName, repoID)

	m.heartbeatMutex.Lock()
	defer m.heartbeatMutex.Unlock()

	delete(m.heartbeatMap, key)
}

// recoverOrphanedScans detects and recovers orphaned scans on startup
func (m *ScanStatusManager) recoverOrphanedScans() {
	log.Println("Checking for orphaned scans...")

	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	orphanedCount := 0
	staleCount := 0

	for key, status := range m.statusMap {
		if status.InProgress {
			timeSinceStart := time.Since(status.LastScanTime)
			isOrphaned := false
			reason := ""

			// More aggressive detection for stale tasks after application restart
			// If the scan was started before the application startup time, it's definitely stale
			if status.LastScanTime.Before(m.startupTime) {
				isOrphaned = true
				reason = fmt.Sprintf("Scan started before application restart (scan: %s, startup: %s)",
					status.LastScanTime.Format("15:04:05"), m.startupTime.Format("15:04:05"))
				staleCount++
			} else if timeSinceStart > m.scanTimeout {
				// Traditional timeout-based detection
				isOrphaned = true
				reason = fmt.Sprintf("Scan exceeded timeout (running for %v)", timeSinceStart)
				orphanedCount++
			}

			if isOrphaned {
				log.Printf("Detected orphaned scan for %s: %s", key, reason)

				// Mark scan as interrupted due to application restart
				status.InProgress = false
				status.FailedSources = append(status.FailedSources, models.SourceScanFailure{
					SourceID:   "system",
					SourceName: "System Recovery",
					Error:      fmt.Sprintf("Scan interrupted: %s", reason),
					FailedAt:   time.Now(),
				})

				// Update the status
				m.statusMap[key] = status

				// Save to disk
				if err := m.saveStatusToDisk(status); err != nil {
					log.Printf("Warning: Failed to save recovered status to disk: %v", err)
				}
			}
		}
	}

	totalRecovered := orphanedCount + staleCount
	if totalRecovered > 0 {
		log.Printf("Recovered %d orphaned scans (%d stale from restart, %d timeout-based)",
			totalRecovered, staleCount, orphanedCount)
	} else {
		log.Println("No orphaned scans found")
	}
}

// CleanupStaleTasks manually cleans up tasks that are marked as in progress but are likely stale
// This can be called from the API to force cleanup of stale tasks
func (m *ScanStatusManager) CleanupStaleTasks() (int, error) {
	log.Println("Manual cleanup of stale tasks requested...")

	m.statusMutex.Lock()
	defer m.statusMutex.Unlock()

	cleanedCount := 0
	now := time.Now()

	for key, status := range m.statusMap {
		if status.InProgress {
			// Consider a task stale if:
			// 1. It was started more than 5 minutes ago and has no recent heartbeat
			// 2. It was started before application startup
			isStale := false
			reason := ""

			if status.LastScanTime.Before(m.startupTime) {
				isStale = true
				reason = "Started before application restart"
			} else {
				// Check heartbeat
				m.heartbeatMutex.RLock()
				lastHeartbeat, hasHeartbeat := m.heartbeatMap[key]
				m.heartbeatMutex.RUnlock()

				if hasHeartbeat {
					if now.Sub(lastHeartbeat) > 5*time.Minute {
						isStale = true
						reason = fmt.Sprintf("No heartbeat for %v", now.Sub(lastHeartbeat))
					}
				} else {
					// No heartbeat and running for more than 5 minutes
					if now.Sub(status.LastScanTime) > 5*time.Minute {
						isStale = true
						reason = fmt.Sprintf("No heartbeat and running for %v", now.Sub(status.LastScanTime))
					}
				}
			}

			if isStale {
				log.Printf("Cleaning up stale task %s: %s", key, reason)

				// Mark as interrupted
				status.InProgress = false
				status.FailedSources = append(status.FailedSources, models.SourceScanFailure{
					SourceID:   "system",
					SourceName: "Manual Cleanup",
					Error:      fmt.Sprintf("Task cleaned up: %s", reason),
					FailedAt:   now,
				})

				// Update the status
				m.statusMap[key] = status

				// Save to disk
				if err := m.saveStatusToDisk(status); err != nil {
					log.Printf("Warning: Failed to save cleaned status to disk: %v", err)
				}

				cleanedCount++
			}
		}
	}

	log.Printf("Manual cleanup completed: %d stale tasks cleaned", cleanedCount)
	return cleanedCount, nil
}

// CheckForOrphanedScans periodically checks for orphaned scans during runtime
func (m *ScanStatusManager) CheckForOrphanedScans() {
	m.statusMutex.RLock()
	m.heartbeatMutex.RLock()

	var orphanedScans []string
	now := time.Now()

	for key, status := range m.statusMap {
		if status.InProgress {
			// Check if we have a recent heartbeat
			if lastHeartbeat, exists := m.heartbeatMap[key]; exists {
				if now.Sub(lastHeartbeat) > m.scanTimeout {
					orphanedScans = append(orphanedScans, key)
				}
			} else {
				// No heartbeat found for an in-progress scan
				timeSinceStart := now.Sub(status.LastScanTime)
				if timeSinceStart > m.scanTimeout {
					orphanedScans = append(orphanedScans, key)
				}
			}
		}
	}

	m.heartbeatMutex.RUnlock()
	m.statusMutex.RUnlock()

	// Handle orphaned scans
	if len(orphanedScans) > 0 {
		log.Printf("Found %d orphaned scans during runtime check", len(orphanedScans))

		m.statusMutex.Lock()
		for _, key := range orphanedScans {
			if status, exists := m.statusMap[key]; exists && status.InProgress {
				log.Printf("Marking orphaned scan as failed: %s", key)

				status.InProgress = false
				status.FailedSources = append(status.FailedSources, models.SourceScanFailure{
					SourceID:   "system",
					SourceName: "System Monitor",
					Error:      "Scan orphaned due to missing heartbeat",
					FailedAt:   time.Now(),
				})

				m.statusMap[key] = status

				// Save to disk
				if err := m.saveStatusToDisk(status); err != nil {
					log.Printf("Warning: Failed to save orphaned scan status to disk: %v", err)
				}

				// Remove heartbeat
				m.heartbeatMutex.Lock()
				delete(m.heartbeatMap, key)
				m.heartbeatMutex.Unlock()
			}
		}
		m.statusMutex.Unlock()
	}
}
