package services

import (
	"context"
	"fmt"
	"io/fs"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"adgitops-ui/src/backend/models"

	"github.com/google/uuid"
)

// GitSourceHandler handles Git repository sources
type GitSourceHandler struct {
	BaseSourceHandler
	clonePath            string
	logger               *ScanLogger // Scan logger for UI integration
	scanID               string      // Current scan ID for logging
	lastCommit           string      // Last known commit hash
	lastSyncTime         time.Time   // Last time we checked for updates
	syncInProgress       bool        // Flag to prevent concurrent syncs
	successfulAuthMethod string      // Store the successful authentication method
}

// NewGitSourceHandler creates a new Git source handler
func NewGitSourceHandler() *GitSourceHandler {
	return &GitSourceHandler{}
}

// SetScanLogger sets the scan logger and scan ID for UI integration
func (g *GitSourceHandler) SetScanLogger(logger *ScanLogger, scanID string) {
	g.logger = logger
	g.scanID = scanID
}

// loadPersistedState loads the last commit hash and sync time from files
func (g *GitSourceHandler) loadPersistedState() {
	// Load last commit hash
	commitFile := filepath.Join(g.clonePath, "COMMIT_HASH")
	if data, err := os.ReadFile(commitFile); err == nil {
		g.lastCommit = strings.TrimSpace(string(data))
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "load_state", fmt.Sprintf("Loaded last commit hash: %s", g.lastCommit), nil)
		}
	}

	// Load last sync time
	syncFile := filepath.Join(g.clonePath, "LAST_SYNC")
	if data, err := os.ReadFile(syncFile); err == nil {
		if syncTime, err := time.Parse(time.RFC3339, strings.TrimSpace(string(data))); err == nil {
			g.lastSyncTime = syncTime
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "load_state", fmt.Sprintf("Loaded last sync time: %s", g.lastSyncTime.Format(time.RFC3339)), nil)
			}
		}
	}
}

// savePersistedState saves the current commit hash and sync time to files
func (g *GitSourceHandler) savePersistedState() error {
	// Ensure directory exists
	if err := os.MkdirAll(g.clonePath, 0755); err != nil {
		return fmt.Errorf("failed to create repository directory: %w", err)
	}

	// Save commit hash
	if g.lastCommit != "" {
		commitFile := filepath.Join(g.clonePath, "COMMIT_HASH")
		if err := os.WriteFile(commitFile, []byte(g.lastCommit), 0644); err != nil {
			return fmt.Errorf("failed to save commit hash: %w", err)
		}
	}

	// Save sync time
	g.lastSyncTime = time.Now()
	syncFile := filepath.Join(g.clonePath, "LAST_SYNC")
	if err := os.WriteFile(syncFile, []byte(g.lastSyncTime.Format(time.RFC3339)), 0644); err != nil {
		return fmt.Errorf("failed to save sync time: %w", err)
	}

	return nil
}

// needsSync determines if the repository needs to be synced based on time and commit hash
func (g *GitSourceHandler) needsSync(ctx context.Context) (bool, error) {
	// Get sync interval from source configuration (default to 5 minutes)
	syncInterval := 5 * time.Minute
	if g.source.GitConfig != nil && g.source.GitConfig.SyncInterval > 0 {
		syncInterval = time.Duration(g.source.GitConfig.SyncInterval) * time.Second
	}

	// Check if enough time has passed since last sync
	if time.Since(g.lastSyncTime) < syncInterval {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_check", fmt.Sprintf("Skipping sync - last sync was %v ago (interval: %v)", time.Since(g.lastSyncTime), syncInterval), nil)
		}
		return false, nil
	}

	// Check if repository exists locally
	if _, err := os.Stat(filepath.Join(g.clonePath, ".git")); os.IsNotExist(err) {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_check", "Repository doesn't exist locally - needs initial clone", nil)
		}
		return true, nil
	}

	// Check remote commit hash
	remoteCommit, err := g.getRemoteCommitHash(ctx)
	if err != nil {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogWarning(g.scanID, "sync_check", "Failed to get remote commit hash, will sync anyway", err.Error())
		}
		return true, nil // Sync on error to be safe
	}

	// Compare with local commit hash
	needsSync := g.lastCommit != remoteCommit
	if g.logger != nil && g.scanID != "" {
		if needsSync {
			g.logger.LogStep(g.scanID, "sync_check", fmt.Sprintf("Repository needs sync - local: %s, remote: %s", g.lastCommit, remoteCommit), nil)
		} else {
			g.logger.LogStep(g.scanID, "sync_check", fmt.Sprintf("Repository is up to date - commit: %s", g.lastCommit), nil)
		}
	}

	return needsSync, nil
}

// getRemoteCommitHash gets the latest commit hash from the remote repository
func (g *GitSourceHandler) getRemoteCommitHash(ctx context.Context) (string, error) {
	// Use git ls-remote to get the latest commit hash without cloning
	args := []string{"ls-remote", g.source.GitConfig.RepoURL, g.source.GitConfig.Branch}
	cmd := g.createAuthenticatedGitCommand(ctx, args...)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("git ls-remote failed: %s", string(output))
	}

	// Parse the output to get the commit hash
	lines := strings.Split(strings.TrimSpace(string(output)), "\n")
	if len(lines) == 0 {
		return "", fmt.Errorf("no output from git ls-remote")
	}

	// The first line should contain the commit hash
	parts := strings.Fields(lines[0])
	if len(parts) == 0 {
		return "", fmt.Errorf("invalid output from git ls-remote")
	}

	commitHash := parts[0]
	if len(commitHash) < 7 {
		return "", fmt.Errorf("invalid commit hash: %s", commitHash)
	}

	return commitHash, nil
}

// Initialize initializes the Git source handler
func (g *GitSourceHandler) Initialize(source models.UsageSource) error {
	if err := g.BaseSourceHandler.Initialize(source); err != nil {
		return err
	}

	if source.GitConfig == nil {
		return models.NewValidationError("gitConfig", "git configuration is required")
	}

	// Set up persistent clone path (not in temp directory)
	if source.GitConfig.ClonePath != "" {
		g.clonePath = source.GitConfig.ClonePath
	} else {
		// Use persistent storage directory similar to repository system
		dataDir := "data" // TODO: Make this configurable
		g.clonePath = filepath.Join(dataDir, "usage-sources", "git-repos", source.ID)
	}

	// Load existing commit hash and sync time if available
	g.loadPersistedState()

	return nil
}

// GetSourceType returns the source type
func (g *GitSourceHandler) GetSourceType() models.SourceType {
	return models.SourceTypeGit
}

// ValidateConfig validates the Git source configuration
func (g *GitSourceHandler) ValidateConfig(source models.UsageSource) error {
	if err := g.BaseSourceHandler.ValidateConfig(source); err != nil {
		return err
	}

	if source.GitConfig == nil {
		return models.NewValidationError("gitConfig", "git configuration is required")
	}

	return source.GitConfig.Validate()
}

// TestConnection tests the connection to the Git repository
func (g *GitSourceHandler) TestConnection(ctx context.Context) error {
	if g.source.GitConfig == nil {
		return fmt.Errorf("git configuration not initialized")
	}

	// For Bitbucket, try multiple authentication formats
	if strings.Contains(g.source.GitConfig.RepoURL, "bitbucket") && g.source.GitConfig.AuthType == "token" {
		return g.testBitbucketConnection(ctx)
	}

	// Standard connection test for other Git providers
	return g.testStandardConnection(ctx)
}

// testBitbucketConnection tries multiple authentication formats for Bitbucket
func (g *GitSourceHandler) testBitbucketConnection(ctx context.Context) error {
	authFormats := []struct {
		name        string
		useHeader   bool
		username    string
		password    string
		description string
	}{
		{"bearer-header", true, "", "", "HTTP Bearer token header (Bitbucket 8.1+)"},
		{"token-as-username", false, g.source.GitConfig.Token, "", "Token as username (legacy)"},
		{"x-token-auth", false, "x-token-auth", g.source.GitConfig.Token, "x-token-auth format (legacy)"},
		{"token-as-password", false, "", g.source.GitConfig.Token, "Token as password (legacy)"},
	}

	for i, format := range authFormats {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "bitbucket_auth_test", fmt.Sprintf("Trying Bitbucket auth format %d: %s - %s", i+1, format.name, format.description), nil)
		}

		var cmd *exec.Cmd
		var cmdStr string

		if format.useHeader {
			// Use HTTP Bearer token header for Bitbucket Server 8.1+
			authHeader := fmt.Sprintf("Authorization: Bearer %s", g.source.GitConfig.Token)
			cmd = exec.CommandContext(ctx, "git",
				"-c", "credential.helper=", // Disable credential helper
				"-c", "core.askPass=", // Disable askpass
				"-c", fmt.Sprintf("http.extraHeader=%s", authHeader), // Add Bearer token header
				"ls-remote", g.source.GitConfig.RepoURL)

			cmdStr = fmt.Sprintf("git -c credential.helper= -c core.askPass= -c \"http.extraHeader=Authorization: Bearer ***\" ls-remote %s", g.source.GitConfig.RepoURL)
		} else {
			// Use URL-based authentication (legacy methods)
			authenticatedURL := g.buildAuthenticatedURL(g.source.GitConfig.RepoURL, format.username, format.password)
			cmd = exec.CommandContext(ctx, "git",
				"-c", "credential.helper=", // Disable credential helper
				"-c", "core.askPass=", // Disable askpass
				"ls-remote", authenticatedURL)

			cmdStr = g.sanitizeCommandForLogging(cmd)
		}

		// Set up environment
		g.setupGitAuthEnvironment(cmd)

		// Log the command being executed (without sensitive credentials)
		sanitizedCmd := g.sanitizeForLogging(cmdStr)
		log.Printf("Executing Git command for source %s (format: %s): %s", g.source.Name, format.name, sanitizedCmd)

		output, err := cmd.CombinedOutput()
		if err == nil {
			log.Printf("Git connection test successful for source %s using format: %s", g.source.Name, format.name)
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "bitbucket_auth_success", fmt.Sprintf("Bitbucket authentication successful with format: %s", format.name), nil)
			}

			// Store the successful authentication method for later use
			g.successfulAuthMethod = format.name
			g.updateStatus(true, nil)
			return nil
		}

		// Log the failure and try next format
		sanitizedOutput := g.sanitizeForLogging(string(output))
		log.Printf("Git ls-remote failed for source %s with format %s: %v\nOutput: %s", g.source.Name, format.name, err, sanitizedOutput)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogWarning(g.scanID, "bitbucket_auth_attempt", fmt.Sprintf("Auth format '%s' failed: %s", format.name, sanitizedOutput), "")
		}
	}

	// All formats failed
	errorMsg := "All Bitbucket authentication formats failed"
	if g.logger != nil && g.scanID != "" {
		g.logger.LogError(g.scanID, "bitbucket_auth_failed", errorMsg, "Tried Bearer header (8.1+), token-as-username, x-token-auth, and token-as-password formats", g.source.ID)
	}
	g.updateStatus(false, fmt.Errorf(errorMsg))
	return fmt.Errorf(errorMsg)
}

// testStandardConnection performs standard Git connection test
func (g *GitSourceHandler) testStandardConnection(ctx context.Context) error {
	// Test by doing a shallow clone or ls-remote with credential helper disabled
	cmd := exec.CommandContext(ctx, "git",
		"-c", "credential.helper=", // Disable credential helper
		"-c", "core.askPass=", // Disable askpass
		"ls-remote", g.source.GitConfig.RepoURL)

	// Set up authentication if needed
	if err := g.setupGitAuth(cmd); err != nil {
		errorMsg := fmt.Sprintf("Git auth setup failed for source %s: %v", g.source.Name, err)
		log.Printf(errorMsg)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogError(g.scanID, "git_auth_setup", "Failed to setup Git authentication", err.Error(), g.source.ID)
		}
		g.updateStatus(false, err)
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	// Log the command being executed (without sensitive credentials)
	cmdStr := g.sanitizeCommandForLogging(cmd)
	sanitizedCmd := g.sanitizeForLogging(cmdStr)
	log.Printf("Executing Git command for source %s: %s", g.source.Name, sanitizedCmd)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_connection_test", fmt.Sprintf("Testing Git connection: %s", sanitizedCmd), nil)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		sanitizedOutput := g.sanitizeForLogging(string(output))
		errorMsg := fmt.Sprintf("Git ls-remote failed for source %s: %v\nOutput: %s", g.source.Name, err, sanitizedOutput)
		log.Printf(errorMsg)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogError(g.scanID, "git_connection_test", "Git connection test failed", sanitizedOutput, g.source.ID)
		}
		g.updateStatus(false, fmt.Errorf("git ls-remote failed: %s", string(output)))
		return fmt.Errorf("failed to connect to git repository: %w", err)
	}

	log.Printf("Git connection test successful for source %s", g.source.Name)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_connection_test", "Git connection test successful", nil)
	}
	g.updateStatus(true, nil)
	return nil
}

// ScanForGroupUsage scans the Git repository for group usage
func (g *GitSourceHandler) ScanForGroupUsage(ctx context.Context, groupName string) ([]models.UsageResult, error) {
	if g.source.GitConfig == nil {
		return nil, fmt.Errorf("git configuration not initialized")
	}

	// Clone or update the repository with timeout
	if err := g.cloneOrUpdateRepository(ctx); err != nil {
		// If git operations fail but we have a local copy, proceed with scanning
		if _, statErr := os.Stat(filepath.Join(g.clonePath, ".git")); statErr == nil {
			log.Printf("Git operation failed but local repository exists, proceeding with scan: %v", err)
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "git_fallback", "Git operation failed, using local repository copy", nil)
			}
		} else {
			return nil, fmt.Errorf("failed to clone/update repository and no local copy available: %w", err)
		}
	}

	// Determine if we should use chunked processing
	// For repositories with many files, use chunked processing for better progress tracking
	useChunkedProcessing := true // Enable chunked processing by default
	chunkSize := 50              // Process 50 files per chunk for better progress granularity

	var results []models.UsageResult
	var err error

	if useChunkedProcessing {
		// Use chunked processing for better progress tracking
		results, err = g.scanRepositoryFilesWithChunks(ctx, groupName, chunkSize)
	} else {
		// Use traditional processing
		results, err = g.scanRepositoryFilesWithContext(ctx, groupName)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to scan repository files: %w", err)
	}

	// Update results with source information
	for i := range results {
		results[i].ID = uuid.New().String()
		results[i].GroupName = groupName
		results[i].SourceID = g.source.ID
		results[i].SourceName = g.source.Name
		results[i].SourceType = models.SourceTypeGit
		results[i].DetectedAt = time.Now()

		// Add Git-specific metadata
		if commitHash, err := g.getLatestCommitHash(); err == nil {
			results[i].CommitHash = commitHash
		}
		results[i].Branch = g.source.GitConfig.Branch
	}

	return results, nil
}

// Cleanup cleans up resources
func (g *GitSourceHandler) Cleanup() error {
	// Optionally remove the clone directory
	// For now, we'll keep it for performance (incremental updates)
	return nil
}

// createAuthenticatedGitCommand creates a Git command with the appropriate authentication method
func (g *GitSourceHandler) createAuthenticatedGitCommand(ctx context.Context, args ...string) *exec.Cmd {
	var cmd *exec.Cmd

	// Use the successful authentication method if available, otherwise default to Bearer header
	authMethod := g.successfulAuthMethod
	if authMethod == "" {
		authMethod = "bearer-header" // Default to modern Bitbucket authentication
	}

	if strings.Contains(g.source.GitConfig.RepoURL, "bitbucket") && authMethod == "bearer-header" {
		// Use HTTP Bearer token header for Bitbucket Server 8.1+
		authHeader := fmt.Sprintf("Authorization: Bearer %s", g.source.GitConfig.Token)
		gitArgs := []string{
			"-c", "credential.helper=", // Disable credential helper
			"-c", "core.askPass=", // Disable askpass
			"-c", fmt.Sprintf("http.extraHeader=%s", authHeader), // Add Bearer token header
		}
		gitArgs = append(gitArgs, args...)
		cmd = exec.CommandContext(ctx, "git", gitArgs...)
	} else {
		// Use standard authentication setup
		gitArgs := []string{
			"-c", "credential.helper=", // Disable credential helper
			"-c", "core.askPass=", // Disable askpass
		}
		gitArgs = append(gitArgs, args...)
		cmd = exec.CommandContext(ctx, "git", gitArgs...)

		// Set up URL-based authentication if needed
		g.setupGitAuth(cmd)
	}

	// Set up environment
	g.setupGitAuthEnvironment(cmd)

	return cmd
}

// cloneOrUpdateRepository clones or updates the Git repository using smart sync logic
func (g *GitSourceHandler) cloneOrUpdateRepository(ctx context.Context) error {
	// Create a timeout context for git operations (5 minutes max)
	gitCtx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()

	// Prevent concurrent syncs
	if g.syncInProgress {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_check", "Sync already in progress, skipping", nil)
		}
		return nil
	}

	g.syncInProgress = true
	defer func() {
		g.syncInProgress = false
	}()

	// Check if we need to sync
	needsSync, err := g.needsSync(ctx)
	if err != nil {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogWarning(g.scanID, "sync_check", "Failed to check if sync is needed, proceeding with sync", err.Error())
		}
		needsSync = true // Sync on error to be safe
	}

	if !needsSync {
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_skip", "Repository is up to date, skipping sync", nil)
		}
		return nil
	}

	// Check if repository already exists locally
	if _, err := os.Stat(filepath.Join(g.clonePath, ".git")); os.IsNotExist(err) {
		// Repository doesn't exist, clone it
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_action", "Repository doesn't exist locally, cloning", nil)
		}
		if err := g.cloneRepository(gitCtx); err != nil {
			return err
		}
	} else if err != nil {
		return fmt.Errorf("failed to check repository status: %w", err)
	} else {
		// Repository exists, try to update it
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "sync_action", "Repository exists locally, attempting update", nil)
		}
		if err := g.updateRepository(gitCtx); err != nil {
			// Update failed, but we can still use the local repository
			log.Printf("Git update failed for source %s, continuing with local repository: %v", g.source.Name, err)
			if g.logger != nil && g.scanID != "" {
				g.logger.LogWarning(g.scanID, "git_update_failed",
					"Failed to update repository, using local copy", err.Error())
			}
			// Don't return error - continue with local repository
		} else {
			log.Printf("Successfully updated repository %s", g.source.Name)
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "sync_action", "Repository updated successfully", nil)
			}
		}
	}

	// Update the commit hash and save state
	if remoteCommit, err := g.getRemoteCommitHash(gitCtx); err == nil {
		g.lastCommit = remoteCommit
		if err := g.savePersistedState(); err != nil {
			if g.logger != nil && g.scanID != "" {
				g.logger.LogWarning(g.scanID, "save_state", "Failed to save repository state", err.Error())
			}
		} else {
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "save_state", fmt.Sprintf("Saved repository state - commit: %s", g.lastCommit), nil)
			}
		}
	}

	return nil
}

// cloneRepository clones the Git repository
func (g *GitSourceHandler) cloneRepository(ctx context.Context) error {
	// Create parent directory
	if err := os.MkdirAll(filepath.Dir(g.clonePath), 0755); err != nil {
		log.Printf("Failed to create clone directory for source %s: %v", g.source.Name, err)
		return fmt.Errorf("failed to create clone directory: %w", err)
	}

	// Clone the repository using the authenticated command helper
	args := []string{"clone", "--depth", "1"}
	if g.source.GitConfig.Branch != "" {
		args = append(args, "--branch", g.source.GitConfig.Branch)
	}

	// For Bitbucket with Bearer authentication, use the original URL
	// For other methods, the helper will handle URL modification
	if strings.Contains(g.source.GitConfig.RepoURL, "bitbucket") && g.successfulAuthMethod == "bearer-header" {
		args = append(args, g.source.GitConfig.RepoURL, g.clonePath)
	} else {
		args = append(args, g.source.GitConfig.RepoURL, g.clonePath)
	}

	cmd := g.createAuthenticatedGitCommand(ctx, args...)

	// Log the command being executed (without sensitive credentials)
	cmdStr := g.sanitizeCommandForLogging(cmd)
	sanitizedCmd := g.sanitizeForLogging(cmdStr)
	log.Printf("Executing Git clone for source %s: %s", g.source.Name, sanitizedCmd)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_clone", fmt.Sprintf("Cloning Git repository: %s", sanitizedCmd), nil)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		sanitizedOutput := g.sanitizeForLogging(string(output))
		errorMsg := fmt.Sprintf("Git clone failed for source %s: %v\nOutput: %s", g.source.Name, err, sanitizedOutput)
		log.Printf(errorMsg)
		if g.logger != nil && g.scanID != "" {
			g.logger.LogError(g.scanID, "git_clone", "Git clone operation failed", sanitizedOutput, g.source.ID)
		}
		return fmt.Errorf("git clone failed: %s", string(output))
	}

	log.Printf("Successfully cloned repository %s to %s", g.source.GitConfig.RepoURL, g.clonePath)
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "git_clone", "Git repository cloned successfully", nil)
	}
	return nil
}

// updateRepository updates the existing Git repository
func (g *GitSourceHandler) updateRepository(ctx context.Context) error {
	// Change to repository directory with credential helper disabled
	cmd := exec.CommandContext(ctx, "git",
		"-c", "credential.helper=", // Disable credential helper
		"-c", "core.askPass=", // Disable askpass
		"pull", "origin", g.source.GitConfig.Branch)
	cmd.Dir = g.clonePath

	// Set up authentication
	if err := g.setupGitAuth(cmd); err != nil {
		return fmt.Errorf("failed to setup git authentication: %w", err)
	}

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("git pull failed: %s", string(output))
	}

	log.Printf("Successfully updated repository %s", g.source.GitConfig.RepoURL)
	return nil
}

// setupGitAuthEnvironment sets up only the environment variables for Git authentication
func (g *GitSourceHandler) setupGitAuthEnvironment(cmd *exec.Cmd) {
	// Always disable interactive prompts, credential helpers, and set timeout
	globalConfigPath := "/dev/null"
	if strings.Contains(strings.ToLower(os.Getenv("OS")), "windows") {
		globalConfigPath = "NUL" // Windows equivalent of /dev/null
	}

	cmd.Env = append(os.Environ(),
		"GIT_TERMINAL_PROMPT=0",               // Disable interactive prompts
		"GIT_ASKPASS=echo",                    // Disable password prompts
		"GIT_CONFIG_NOSYSTEM=1",               // Ignore system Git config
		"GIT_CONFIG_GLOBAL="+globalConfigPath, // Ignore global Git config
		"GIT_CREDENTIAL_HELPER=",              // Disable credential helpers
		"GIT_SSH_COMMAND=ssh -o BatchMode=yes -o StrictHostKeyChecking=no", // Non-interactive SSH
	)
}

// setupGitAuth sets up Git authentication for the command
func (g *GitSourceHandler) setupGitAuth(cmd *exec.Cmd) error {
	config := g.source.GitConfig

	// Always disable interactive prompts, credential helpers, and set timeout
	globalConfigPath := "/dev/null"
	if strings.Contains(strings.ToLower(os.Getenv("OS")), "windows") {
		globalConfigPath = "NUL" // Windows equivalent of /dev/null
	}

	cmd.Env = append(os.Environ(),
		"GIT_TERMINAL_PROMPT=0",               // Disable interactive prompts
		"GIT_ASKPASS=echo",                    // Disable password prompts
		"GIT_CONFIG_NOSYSTEM=1",               // Ignore system Git config
		"GIT_CONFIG_GLOBAL="+globalConfigPath, // Ignore global Git config
		"GIT_CREDENTIAL_HELPER=",              // Disable credential helpers
		"GIT_SSH_COMMAND=ssh -o BatchMode=yes -o StrictHostKeyChecking=no", // Non-interactive SSH
	)

	switch config.AuthType {
	case "token":
		if config.Token != "" {
			var authenticatedURL string

			// Check if this is a Bitbucket URL (requires special handling)
			if strings.Contains(config.RepoURL, "bitbucket") {
				// Try multiple Bitbucket authentication formats
				// Format 1: token as username (most common for Bitbucket Server)
				authenticatedURL = g.buildAuthenticatedURL(config.RepoURL, config.Token, "")
				log.Printf("Using Bitbucket Server token as username authentication for Git repository")
				if g.logger != nil && g.scanID != "" {
					g.logger.LogStep(g.scanID, "auth_setup", "Using Bitbucket token as username authentication", nil)
				}
			} else {
				// For GitHub/GitLab, use token as username with empty password
				authenticatedURL = g.buildAuthenticatedURL(config.RepoURL, config.Token, "")
				log.Printf("Using token authentication for Git repository")
			}

			// Replace the original URL in the command arguments
			for i, arg := range cmd.Args {
				if arg == config.RepoURL {
					cmd.Args[i] = authenticatedURL
					break
				}
			}
		}
	case "basic":
		if config.Username != "" && config.Password != "" {
			// For basic auth, modify the URL to include credentials
			authenticatedURL := g.buildAuthenticatedURL(config.RepoURL, config.Username, config.Password)

			// Replace the original URL in the command arguments
			for i, arg := range cmd.Args {
				if arg == config.RepoURL {
					cmd.Args[i] = authenticatedURL
					break
				}
			}

			log.Printf("Using basic authentication for Git repository (username: %s)", config.Username)
		}
	case "none":
		// No authentication needed
		log.Printf("Using no authentication for Git repository")
	default:
		return fmt.Errorf("unsupported auth type: %s", config.AuthType)
	}

	return nil
}

// buildAuthenticatedURL builds a Git URL with embedded credentials
func (g *GitSourceHandler) buildAuthenticatedURL(repoURL, username, password string) string {
	// Parse the URL to inject credentials
	if strings.HasPrefix(repoURL, "https://") {
		// Remove https:// prefix
		urlWithoutScheme := strings.TrimPrefix(repoURL, "https://")

		// Build authenticated URL
		if password != "" {
			return fmt.Sprintf("https://%s:%s@%s", username, password, urlWithoutScheme)
		} else {
			// Token-based auth (token as username, empty password)
			return fmt.Sprintf("https://%s@%s", username, urlWithoutScheme)
		}
	}

	// For SSH URLs or other schemes, return as-is
	return repoURL
}

// sanitizeCommandForLogging creates a safe version of the command for logging (removes credentials)
func (g *GitSourceHandler) sanitizeCommandForLogging(cmd *exec.Cmd) string {
	args := make([]string, len(cmd.Args))
	copy(args, cmd.Args)

	// Replace any URLs that contain credentials with sanitized versions
	for i, arg := range args {
		if strings.Contains(arg, "@") && (strings.HasPrefix(arg, "https://") || strings.HasPrefix(arg, "http://")) {
			// This looks like a URL with credentials, sanitize it
			if strings.HasPrefix(arg, "https://") {
				// Extract the part after the credentials
				parts := strings.SplitN(arg, "@", 2)
				if len(parts) == 2 {
					args[i] = "https://***:***@" + parts[1]
				}
			} else if strings.HasPrefix(arg, "http://") {
				parts := strings.SplitN(arg, "@", 2)
				if len(parts) == 2 {
					args[i] = "http://***:***@" + parts[1]
				}
			}
		}

		// Sanitize HTTP headers containing Authorization
		if strings.Contains(arg, "Authorization:") {
			arg = regexp.MustCompile(`Authorization:\s*Bearer\s+[^\s]+`).ReplaceAllString(arg, "Authorization: Bearer ***")
			arg = regexp.MustCompile(`Authorization:\s*Basic\s+[^\s]+`).ReplaceAllString(arg, "Authorization: Basic ***")
			args[i] = arg
		}

		// Sanitize http.extraHeader with Authorization
		if strings.Contains(arg, "http.extraHeader=Authorization:") {
			args[i] = regexp.MustCompile(`http\.extraHeader=Authorization:\s*Bearer\s+[^\s"']+`).ReplaceAllString(arg, "http.extraHeader=Authorization: Bearer ***")
		}
	}

	return strings.Join(args, " ")
}

// sanitizeForLogging removes sensitive information from any string for logging
func (g *GitSourceHandler) sanitizeForLogging(text string) string {
	if text == "" {
		return text
	}

	// Sanitize URLs with credentials
	text = regexp.MustCompile(`://[^@/\s]+@`).ReplaceAllString(text, "://***:***@")

	// Sanitize Authorization headers
	text = regexp.MustCompile(`Authorization:\s*Bearer\s+[^\s]+`).ReplaceAllString(text, "Authorization: Bearer ***")
	text = regexp.MustCompile(`Authorization:\s*Basic\s+[^\s]+`).ReplaceAllString(text, "Authorization: Basic ***")

	// Sanitize tokens in HTTP headers
	text = regexp.MustCompile(`http\.extraHeader=Authorization:\s*Bearer\s+[^\s"']+`).ReplaceAllString(text, "http.extraHeader=Authorization: Bearer ***")

	// Sanitize long token-like strings (but preserve short words and common patterns)
	words := strings.Fields(text)
	for i, word := range words {
		// Clean word of punctuation for checking
		cleanWord := regexp.MustCompile(`[^\w-]`).ReplaceAllString(word, "")
		if len(cleanWord) > 20 && regexp.MustCompile(`^[a-zA-Z0-9_-]+$`).MatchString(cleanWord) {
			// Replace the token part while preserving surrounding punctuation
			words[i] = regexp.MustCompile(`[a-zA-Z0-9_-]{20,}`).ReplaceAllString(word, "***")
		}
	}

	return strings.Join(words, " ")
}

// scanRepositoryFilesWithChunks scans repository files using chunked processing for better progress tracking
func (g *GitSourceHandler) scanRepositoryFilesWithChunks(ctx context.Context, groupName string, chunkSize int) ([]models.UsageResult, error) {
	var allResults []models.UsageResult

	// First pass: collect all files that match patterns
	var filesToProcess []string
	err := filepath.WalkDir(g.clonePath, func(path string, d fs.DirEntry, err error) error {
		if err != nil || d.IsDir() {
			return nil
		}
		if d.Name() == ".git" {
			return filepath.SkipDir
		}
		relPath, _ := filepath.Rel(g.clonePath, path)
		if matchesPatterns(relPath, g.source.IncludePatterns, g.source.ExcludePatterns) {
			filesToProcess = append(filesToProcess, path)
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to collect files for processing: %w", err)
	}

	totalFiles := len(filesToProcess)
	if totalFiles == 0 {
		return allResults, nil
	}

	// Calculate chunk configuration
	if chunkSize <= 0 {
		chunkSize = 100 // Default chunk size
	}
	totalChunks := (totalFiles + chunkSize - 1) / chunkSize

	// Log initial progress with chunked processing info
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "chunked_scan_start", fmt.Sprintf("Starting chunked file scan of %d files in %d chunks", totalFiles, totalChunks), &models.Progress{
			Current:     0,
			Total:       totalFiles,
			Percentage:  0,
			Description: fmt.Sprintf("Processing %d files in %d chunks", totalFiles, totalChunks),
			TotalChunks: totalChunks,
			ChunkSize:   chunkSize,
		})

		// Notify progress broadcaster about chunked processing start
		if g.logger.progressBroadcaster != nil {
			chunkedConfig := &models.ChunkedProcessingConfig{
				Enabled:                true,
				ChunkSize:              chunkSize,
				MaxConcurrentChunks:    1, // Sequential processing for now
				ProgressUpdateInterval: 5,
			}
			g.logger.progressBroadcaster.StartScan(g.scanID, "", groupName, totalFiles, chunkedConfig)
		}
	}

	// Process files in chunks
	var processedFiles int
	for chunkIndex := 0; chunkIndex < totalChunks; chunkIndex++ {
		// Check for context cancellation
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		// Calculate chunk boundaries
		startIdx := chunkIndex * chunkSize
		endIdx := startIdx + chunkSize
		if endIdx > totalFiles {
			endIdx = totalFiles
		}

		chunkFiles := filesToProcess[startIdx:endIdx]
		chunkResults := make([]models.UsageResult, 0)

		// Notify chunk start
		if g.logger != nil && g.scanID != "" && g.logger.progressBroadcaster != nil {
			g.logger.progressBroadcaster.StartChunk(g.scanID, chunkIndex, len(chunkFiles), chunkFiles)
		}

		// Process files in current chunk
		for fileIdx, filePath := range chunkFiles {
			// Check for context cancellation
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			default:
			}

			relPath, err := filepath.Rel(g.clonePath, filePath)
			if err != nil {
				log.Printf("Warning: Failed to get relative path for %s: %v", filePath, err)
				continue
			}

			// Notify file processing start
			if g.logger != nil && g.scanID != "" && g.logger.progressBroadcaster != nil {
				g.logger.progressBroadcaster.UpdateFileProgress(g.scanID, relPath, "processing", 0, "")
			}

			// Read and process file
			content, err := os.ReadFile(filePath)
			if err != nil {
				log.Printf("Warning: Failed to read file %s: %v", filePath, err)
				if g.logger != nil && g.scanID != "" && g.logger.progressBroadcaster != nil {
					g.logger.progressBroadcaster.UpdateFileProgress(g.scanID, relPath, "failed", 0, err.Error())
				}
				continue
			}

			// Search for group usage in content
			results := findGroupUsageInContent(string(content), groupName, relPath)

			// Add file metadata
			for i := range results {
				if info, err := os.Stat(filePath); err == nil {
					results[i].FileSize = info.Size()
				}
				results[i].FileType = getFileExtension(relPath)
			}

			chunkResults = append(chunkResults, results...)
			processedFiles++

			// Notify file processing completion
			if g.logger != nil && g.scanID != "" && g.logger.progressBroadcaster != nil {
				g.logger.progressBroadcaster.UpdateFileProgress(g.scanID, relPath, "completed", len(results), "")
			}

			// Report progress within chunk
			if (fileIdx+1)%5 == 0 || fileIdx == len(chunkFiles)-1 {
				percentage := int((float64(processedFiles) / float64(totalFiles)) * 100)
				if g.logger != nil && g.scanID != "" {
					g.logger.LogStep(g.scanID, "file_scan_progress", fmt.Sprintf("Processed %d/%d files (chunk %d/%d)", processedFiles, totalFiles, chunkIndex+1, totalChunks), &models.Progress{
						Current:      processedFiles,
						Total:        totalFiles,
						Percentage:   percentage,
						Description:  fmt.Sprintf("Processing chunk %d/%d (%d/%d files)", chunkIndex+1, totalChunks, processedFiles, totalFiles),
						CurrentFile:  relPath,
						CurrentChunk: chunkIndex,
						TotalChunks:  totalChunks,
						ChunkSize:    chunkSize,
					})
				}
			}
		}

		// Add chunk results to total results
		allResults = append(allResults, chunkResults...)

		// Notify chunk completion
		if g.logger != nil && g.scanID != "" && g.logger.progressBroadcaster != nil {
			g.logger.progressBroadcaster.CompleteChunk(g.scanID, chunkIndex)
		}

		// Log chunk completion
		if g.logger != nil && g.scanID != "" {
			g.logger.LogStep(g.scanID, "chunk_complete", fmt.Sprintf("Completed chunk %d/%d (%d results found)", chunkIndex+1, totalChunks, len(chunkResults)), &models.Progress{
				Current:      processedFiles,
				Total:        totalFiles,
				Percentage:   int((float64(processedFiles) / float64(totalFiles)) * 100),
				Description:  fmt.Sprintf("Completed chunk %d/%d", chunkIndex+1, totalChunks),
				CurrentChunk: chunkIndex,
				TotalChunks:  totalChunks,
				ChunkSize:    chunkSize,
			})
		}
	}

	return allResults, nil
}

// scanRepositoryFilesWithContext scans repository files for group usage with context support
func (g *GitSourceHandler) scanRepositoryFilesWithContext(ctx context.Context, groupName string) ([]models.UsageResult, error) {
	var allResults []models.UsageResult

	// First pass: count total files for progress tracking
	var totalFiles int
	filepath.WalkDir(g.clonePath, func(path string, d fs.DirEntry, err error) error {
		if err != nil || d.IsDir() {
			return nil
		}
		if d.Name() == ".git" {
			return filepath.SkipDir
		}
		relPath, _ := filepath.Rel(g.clonePath, path)
		if matchesPatterns(relPath, g.source.IncludePatterns, g.source.ExcludePatterns) {
			totalFiles++
		}
		return nil
	})

	// Log initial progress
	if g.logger != nil && g.scanID != "" {
		g.logger.LogStep(g.scanID, "file_scan_start", fmt.Sprintf("Starting file scan of %d files", totalFiles), &models.Progress{
			Current:     0,
			Total:       totalFiles,
			Percentage:  0,
			Description: fmt.Sprintf("Scanning %d files for group usage", totalFiles),
		})
	}

	var processedFiles int
	err := filepath.WalkDir(g.clonePath, func(path string, d fs.DirEntry, err error) error {
		// Check for context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if err != nil {
			return err
		}

		// Skip .git directory
		if d.IsDir() && d.Name() == ".git" {
			return filepath.SkipDir
		}

		// Skip directories
		if d.IsDir() {
			return nil
		}

		// Get relative path from repository root
		relPath, err := filepath.Rel(g.clonePath, path)
		if err != nil {
			return err
		}

		// Check if file matches include/exclude patterns
		if !matchesPatterns(relPath, g.source.IncludePatterns, g.source.ExcludePatterns) {
			return nil
		}

		// Increment processed files counter
		processedFiles++

		// Read file content
		content, err := os.ReadFile(path)
		if err != nil {
			log.Printf("Warning: Failed to read file %s: %v", path, err)
			return nil // Continue with other files
		}

		// Search for group usage in content
		results := findGroupUsageInContent(string(content), groupName, relPath)

		// Add file metadata
		for i := range results {
			if info, err := d.Info(); err == nil {
				results[i].FileSize = info.Size()
			}
			results[i].FileType = getFileExtension(relPath)
		}

		allResults = append(allResults, results...)

		// Report progress every 10 files or on last file
		if processedFiles%10 == 0 || processedFiles == totalFiles {
			percentage := int((float64(processedFiles) / float64(totalFiles)) * 100)
			if g.logger != nil && g.scanID != "" {
				g.logger.LogStep(g.scanID, "file_scan_progress", fmt.Sprintf("Processed %d/%d files", processedFiles, totalFiles), &models.Progress{
					Current:     processedFiles,
					Total:       totalFiles,
					Percentage:  percentage,
					Description: fmt.Sprintf("Scanning files for group usage (%d/%d)", processedFiles, totalFiles),
				})
			}
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to walk repository directory: %w", err)
	}

	return allResults, nil
}

// scanRepositoryFiles scans repository files for group usage (backward compatibility)
func (g *GitSourceHandler) scanRepositoryFiles(groupName string) ([]models.UsageResult, error) {
	return g.scanRepositoryFilesWithContext(context.Background(), groupName)
}

// getLatestCommitHash gets the latest commit hash
func (g *GitSourceHandler) getLatestCommitHash() (string, error) {
	cmd := exec.Command("git", "rev-parse", "HEAD")
	cmd.Dir = g.clonePath

	output, err := cmd.Output()
	if err != nil {
		return "", err
	}

	return strings.TrimSpace(string(output)), nil
}

// getFileExtension extracts the file extension from a path
func getFileExtension(filePath string) string {
	ext := filepath.Ext(filePath)
	if len(ext) > 1 {
		return ext[1:] // Remove the leading dot
	}
	return ""
}
